@use "sass:map";
@use "@angular/material" as mat;

$primary-palette:(
    50 : #ffffff,
    100 : #e6f5ff,
    200 : #b3e0ff,
    300 : #80ccff,
    400 : #4db8ff,
    500 : #0077cc,
    600 : #008ae6,
    700 : #006bb3,
    800 : #035992,
    900 : #006581cc,
    A100 : #c3e3ff,
    A200 : #006581cc,
    A400 : #5db4ff,
    A700 : #44a8ff,
    contrast: (
        50 : #000000,
        100 : #000000,
        200 : #000000,
        300 : #000000,
        400 : #ffffff,
        500 : #ffffff,
        600 : #ffffff,
        700 : #ffffff,
        800 : #ffffff,
        900 : #ffffff,
        A100 : #000000,
        A200 : #006581cc,
        A400 : #000000,
        A700 : #000000,
    )
);;

$accent-palette:(
    50 : #ffffff,
    100 : #e6f5ff,
    200 : #ccebff,
    300 : #b3e0ff,
    400 : #99d6ff,
    500 : #80ccff,
    600 : #66c2ff,
    700 : #4db8ff,
    800 : #1aa3ff,
    900 : #055275,
    A100 : #c3e3ff,
    A200 : #006581cc,
    A400 : #5db4ff,
    A700 : #44a8ff,
    contrast: (
        50 : #000000,
        100 : #000000,
        200 : #000000,
        300 : #000000,
        400 : #ffffff,
        500 : #ffffff,
        600 : #ffffff,
        700 : #ffffff,
        800 : #ffffff,
        900 : #ffffff,
        A100 : #000000,
        A200 : #000000,
        A400 : #000000,
        A700 : #000000,
    )
);

$my-app-light-primary: mat.define-palette($primary-palette);
$my-app-light-accent: mat.define-palette($accent-palette, A200, A100, A400);

$my-app-light-warn: mat.define-palette(mat.$red-palette);

$my-app-light-theme: mat.define-light-theme(
  (
    color: (
      primary: $my-app-light-primary,
      accent: $my-app-light-accent,
      warn: $my-app-light-warn,
    ),
  )
);