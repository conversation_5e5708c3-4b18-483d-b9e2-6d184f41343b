import { Injectable } from '@angular/core';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';
import { DashboardConfigService } from './dashboard-config.service';

export interface ChartModel {
  id: string;
  title: string;
  type: string;
  data: ChartData;
  options?: any;
}

@Injectable({
  providedIn: 'root'
})
export class ChartRendererService {

  constructor(private configService: DashboardConfigService) {}

  /**
   * Process chart data from backend and prepare for ng2-charts
   */
  processChart(chartData: any): ChartModel {
    return {
      id: chartData.id,
      title: chartData.title,
      type: chartData.type,
      data: this.processChartData(chartData),
      options: this.processChartOptions(chartData)
    };
  }

  /**
   * Process chart data structure
   */
  private processChartData(chartData: any): ChartData {
    // If backend already provides complete chart data, use it
    if (chartData.data) {
      return chartData.data;
    }

    // Fallback processing if needed
    return {
      labels: chartData.labels || [],
      datasets: chartData.datasets || []
    };
  }

  /**
   * Process chart options - merge backend options with defaults
   */
  private processChartOptions(chartData: any): ChartConfiguration['options'] {
    // Get backend-provided options
    const backendOptions = chartData.options || {};
    
    // Merge with global defaults
    const mergedOptions = this.configService.mergeChartOptions(backendOptions);
    
    // Apply chart-type specific enhancements
    return this.enhanceOptionsForChartType(mergedOptions, chartData.type, chartData.title);
  }

  /**
   * Enhance options based on chart type
   */
  private enhanceOptionsForChartType(options: any, chartType: string, title: string): ChartConfiguration['options'] {
    const enhanced = { ...options };

    switch (chartType) {
      case 'doughnut':
      case 'pie':
        return this.enhancePieChartOptions(enhanced, title);
      
      case 'bar':
        return this.enhanceBarChartOptions(enhanced, title);
      
      case 'horizontalBar':
        return this.enhanceHorizontalBarChartOptions(enhanced, title);
      
      case 'line':
        return this.enhanceLineChartOptions(enhanced, title);
      
      default:
        return enhanced;
    }
  }

  /**
   * Enhance pie/doughnut chart options
   */
  private enhancePieChartOptions(options: any, title: string): ChartConfiguration['options'] {
    const currencySymbol = this.configService.getCurrencySymbol();
    
    return {
      ...options,
      plugins: {
        ...options.plugins,
        legend: {
          ...options.plugins?.legend,
          position: 'right'
        },
        tooltip: {
          ...options.plugins?.tooltip,
          callbacks: {
            label: (context: any) => {
              const label = context.label || '';
              const value = context.parsed;
              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
              const percentage = ((value / total) * 100).toFixed(1);
              
              if (this.isCurrencyChart(title)) {
                return `${label}: ${currencySymbol}${value.toLocaleString()} (${percentage}%)`;
              } else {
                return `${label}: ${value.toLocaleString()} (${percentage}%)`;
              }
            }
          }
        }
      }
    };
  }

  /**
   * Enhance bar chart options
   */
  private enhanceBarChartOptions(options: any, title: string): ChartConfiguration['options'] {
    return {
      ...options,
      plugins: {
        ...options.plugins,
        tooltip: {
          ...options.plugins?.tooltip,
          callbacks: {
            label: (context: any) => {
              const label = context.dataset.label || '';
              const value = context.parsed.y;
              
              if (this.isCurrencyChart(title)) {
                return `${label}: ${this.configService.formatLargeNumber(value)}`;
              } else if (this.isPercentageChart(title)) {
                return `${label}: ${value.toFixed(1)}%`;
              } else {
                return `${label}: ${value.toLocaleString()}`;
              }
            }
          }
        }
      },
      scales: {
        ...options.scales,
        y: {
          ...options.scales?.y,
          ticks: {
            ...options.scales?.y?.ticks,
            callback: (value: any) => {
              if (this.isCurrencyChart(title)) {
                return this.configService.formatLargeNumber(value);
              } else if (this.isPercentageChart(title)) {
                return `${value}%`;
              } else {
                return value.toLocaleString();
              }
            }
          }
        }
      }
    };
  }

  /**
   * Enhance horizontal bar chart options
   */
  private enhanceHorizontalBarChartOptions(options: any, title: string): ChartConfiguration['options'] {
    const enhanced = this.enhanceBarChartOptions(options, title);
    
    return {
      ...enhanced,
      indexAxis: 'y' as const,
      plugins: {
        ...enhanced.plugins,
        legend: {
          ...enhanced.plugins?.legend,
          display: false // Usually hide legend for horizontal bars
        }
      },
      scales: {
        x: enhanced.scales?.['y'], // Swap x and y for horizontal
        y: {
          ...enhanced.scales?.['x'],
          grid: { display: false }
        }
      }
    };
  }

  /**
   * Enhance line chart options
   */
  private enhanceLineChartOptions(options: any, _title: string): ChartConfiguration['options'] {
    return {
      ...options,
      interaction: {
        mode: 'index',
        intersect: false
      },
      plugins: {
        ...options.plugins,
        tooltip: {
          ...options.plugins?.tooltip,
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        ...options.scales,
        x: {
          ...options.scales?.x,
          grid: { display: true, color: 'rgba(0,0,0,0.1)' }
        },
        y: {
          ...options.scales?.y,
          grid: { display: true, color: 'rgba(0,0,0,0.1)' }
        }
      }
    };
  }

  /**
   * Check if chart deals with currency values
   */
  private isCurrencyChart(title: string): boolean {
    const currencyKeywords = ['value', 'amount', 'cost', 'price', 'revenue', 'profit', 'loss', '₹'];
    return currencyKeywords.some(keyword => 
      title.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Check if chart deals with percentage values
   */
  private isPercentageChart(title: string): boolean {
    const percentageKeywords = ['percentage', '%', 'rate', 'ratio', 'growth'];
    return percentageKeywords.some(keyword => 
      title.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Get chart type for ng2-charts
   */
  getChartType(type: string): ChartType {
    const typeMap: { [key: string]: ChartType } = {
      'bar': 'bar',
      'horizontalBar': 'bar',
      'line': 'line',
      'doughnut': 'doughnut',
      'pie': 'pie',
      'radar': 'radar',
      'polarArea': 'polarArea'
    };
    
    return typeMap[type] || 'bar';
  }

  /**
   * Get chart CSS class based on type and content
   */
  getChartCssClass(chart: ChartModel): string {
    const classes = ['chart-container'];
    
    // Add type-specific classes
    classes.push(`chart-${chart.type}`);
    
    // Add size classes based on chart type
    if (chart.type === 'line' || chart.title.toLowerCase().includes('trend')) {
      classes.push('full-width');
    } else if (chart.type === 'doughnut' || chart.type === 'pie') {
      classes.push('third-width');
    } else {
      classes.push('half-width');
    }
    
    return classes.join(' ');
  }

  /**
   * Process multiple charts from backend response
   */
  processCharts(chartsData: any[]): ChartModel[] {
    return chartsData.map(chartData => this.processChart(chartData));
  }
}
