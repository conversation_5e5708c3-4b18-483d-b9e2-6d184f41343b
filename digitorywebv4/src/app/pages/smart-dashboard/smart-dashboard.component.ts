import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgChartsModule } from 'ng2-charts';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';

import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { ShareDataService } from '../../services/share-data.service';
import { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';
import { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';

interface SummaryCard {
  icon: string;
  value: string;
  label: string;
  color: string;
  data_type?: string;
}

@Component({
  selector: 'app-smart-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    NgChartsModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './smart-dashboard.component.html',
  styleUrls: ['./smart-dashboard.component.scss']
})
export class SmartDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // User and branch data
  user: any;
  branches: any[] = [];
  filteredBranches: any[] = [];
  selectedLocations: string[] = [];
  
  // Form controls
  locationFilterCtrl = new FormControl('');
  startDate = new FormControl();
  endDate = new FormControl();
  searchQuery = new FormControl('');
  baseDateCtrl = new FormControl();
  selectedDashboard = '';

  // Dashboard data
  summaryCards: SummaryCard[] = [];
  charts: ChartModel[] = [];
  isLoading = true; // Start with loading state
  isConfigLoaded = false;

  // Dynamic configuration data
  dashboardTypes: DashboardType[] = [];
  baseDateOptions: BaseDateOption[] = [];





  constructor(
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private shareDataService: ShareDataService,
    private configService: DashboardConfigService,
    private chartRenderer: ChartRendererService,
    private cdr: ChangeDetectorRef
  ) {
    this.user = this.authService.getCurrentUser();
    this.initializeConfig();
  }

  private initializeConfig(): void {
    // Load dashboard configuration on component initialization
    this.configService.loadConfig().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.configService.setConfig(response.data);
          this.setupDynamicConfigurations(response.data);
        } else {
          this.setupDefaultConfigurations();
        }
        this.isConfigLoaded = true;
        this.cdr.detectChanges();
      },
      error: () => {
        this.setupDefaultConfigurations();
        this.isConfigLoaded = true;
        this.cdr.detectChanges();
      }
    });
  }

  private setupDynamicConfigurations(config: any): void {
    // Set dashboard types
    this.dashboardTypes = config.dashboard_types || [];

    // Set base date options
    this.baseDateOptions = config.base_date_options || [];

    // Set default values from UI config
    const uiConfig = config.ui_config || {};

    // Set default form values
    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';
    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');

    // Set date range based on dashboard type
    this.setDefaultDateRange();

    // Load dashboard data after configuration is set with default selections
    setTimeout(() => {
      this.loadDashboardData();
    }, 100);
  }

  private setupDefaultConfigurations(): void {
    // Fallback configurations if backend fails
    this.dashboardTypes = [
      { value: 'purchase', label: 'Purchase Dashboard' },
      { value: 'inventory', label: 'Inventory Dashboard' },
    ];
    this.baseDateOptions = [
      { value: 'deliveryDate', label: 'Delivery Date' },
      { value: 'orderDate', label: 'Order Date' },
      { value: 'createdDate', label: 'Created Date' }
    ];
    this.selectedDashboard = 'inventory';
    this.baseDateCtrl.setValue('deliveryDate');

    // Set date range based on dashboard type
    this.setDefaultDateRange();

    // Load dashboard data after fallback configuration is set with default selections
    setTimeout(() => {
      this.loadDashboardData();
    }, 100);
  }

  ngOnInit(): void {
    this.initializeFilters();
    this.loadBranches();
    // Don't load dashboard data immediately - wait for config to load
    // Dashboard data will be loaded after config is ready
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    // Location filter
    this.locationFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterBranches(value || '');
      });
  }

  private loadBranches(): void {
    this.shareDataService.selectedBranchesSource
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.branches = data || [];
        this.filteredBranches = [...this.branches];

        // Select all branches by default
        this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);
      });
  }

  private filterBranches(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredBranches = [...this.branches];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredBranches = this.branches.filter(branch =>
        branch.branchName.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  loadDashboardData(): void {
    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {
      // If required fields are missing, stop loading and show empty state
      this.isLoading = false;
      this.clearDashboardData();
      this.cdr.detectChanges();
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocations || [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate'
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: '',
      use_default_charts: true,
      dashboard_type: this.selectedDashboard
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            this.clearDashboardData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.clearDashboardData();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  private clearDashboardData(): void {
    this.summaryCards = [];
    this.charts = [];
  }

  private processDashboardData(data: any): void {
    // Process summary cards using config service
    this.summaryCards = data.summary_items?.map((item: any) => ({
      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),
      value: item.value,
      label: item.label,
      color: this.configService.getSummaryCardColor(item.data_type),
      data_type: item.data_type
    })) || [];

    // Process charts using chart renderer service
    this.charts = data.charts?.map((chart: any) =>
      this.chartRenderer.processChart(chart)
    ) || [];
  }

  private formatDate(date: Date): string {
    // Fix the date offset issue by using local date components
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private setDefaultDateRange(): void {
    const today = new Date();

    if (this.selectedDashboard === 'inventory') {
      // For inventory dashboard: current month start to current date
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      this.startDate.setValue(startOfMonth);
      this.endDate.setValue(today);
    } else {
      // For purchase dashboard: last 30 days (existing behavior)
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
      this.startDate.setValue(thirtyDaysAgo);
      this.endDate.setValue(today);
    }
  }

  onLocationChange(): void {
    // No automatic API call - user must click Search button
  }

  onDateChange(): void {
    // No automatic API call - user must click Search button
  }

  onDashboardChange(): void {
    // Update date range when dashboard type changes
    this.setDefaultDateRange();
    // No automatic API call - user must click Search button
  }

  searchDashboard(): void {
    // This method is called when Search button is clicked
    this.loadDashboardData();
  }

  resetFilters(): void {
    // Reset all filters to default values
    this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);
    this.setDefaultDateRange();
    this.baseDateCtrl.setValue('deliveryDate');
    this.searchQuery.setValue('');
    this.locationFilterCtrl.setValue('');
    this.filteredBranches = [...this.branches];

    // Load dashboard data with reset filters
    this.loadDashboardData();
  }

  onSearchQuery(): void {
    const query = this.searchQuery.value?.trim();
    if (query) {
      this.loadDashboardDataWithQuery(query);
    } else {
      this.loadDashboardData();
    }
  }

  private loadDashboardDataWithQuery(query: string): void {
    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {
      // If required fields are missing, stop loading and show empty state
      this.isLoading = false;
      this.clearDashboardData();
      this.cdr.detectChanges();
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocations || [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate'
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: query,
      use_default_charts: false,
      dashboard_type: this.selectedDashboard
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            this.clearDashboardData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.clearDashboardData();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Dynamic chart methods using services
  getChartData(chart: ChartModel): ChartData {
    return chart.data;
  }

  getChartType(chart: ChartModel): ChartType {
    return this.chartRenderer.getChartType(chart.type);
  }

  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {
    return chart.options || this.configService.getDefaultChartOptions();
  }

  getChartCssClass(chart: ChartModel): string {
    return this.chartRenderer.getChartCssClass(chart);
  }
}
