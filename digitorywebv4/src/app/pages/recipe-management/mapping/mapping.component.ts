import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MasterDataService } from 'src/app/services/master-data.service';
import { Router } from '@angular/router';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ShareDataService } from 'src/app/services/share-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTabsModule } from '@angular/material/tabs';
import { ReplaySubject, Subject, first, takeUntil } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { InventoryService } from 'src/app/services/inventory.service';
// import { Chart } from 'chart.js';
// import Chart from 'chart.js';
// import Chart, { ChartType, ChartOptions } from 'chart.js/auto';
// import { Chart, registerables } from 'chart.js/auto';
import Chart from 'chart.js/auto';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-mapping',
  standalone: true,
  imports: [
    FormsModule,
    MatChipsModule,
    MatDialogModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatTabsModule,
    MatDividerModule,
    NgxMatSelectSearchModule,
    MatCheckboxModule
  ],
  templateUrl: './mapping.component.html',
  styleUrls: ['./mapping.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MappingComponent {
  baseData: any;
  vendorMappingForm!: FormGroup;
  workAreaMappingForm!: FormGroup;
  public vendorBank: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  public vendor: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public locationBank: any[] = [];
  public locationFilterCtrl: FormControl = new FormControl();
  public location: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public workAreaBank: any[] = [];
  public workAreaFilterCtrl: FormControl = new FormControl();
  public workArea: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public catBank: any[] = [];
  public catFilterCtrl: FormControl = new FormControl();
  public cat: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  public subCatBank: any[] = [];
  public subCatFilterCtrl: FormControl = new FormControl();
  public subCat: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  protected _onDestroy = new Subject<void>();
  newSubCategory: any;
  user: any;
  catAndsubCat = {};
  newCategory: any;
  categories: any[];
  subCategories: any[];
  filteredCatData: any[] = [];
  filteredSubcatData: any[] = [];
  locationList: any;
  selectedTabIndex: number;
  @ViewChild('barChart') barChart: ElementRef;
  @ViewChild('barChart2') barChart2: ElementRef;
  
  chart: any;
  isViewInitialized = false;

  canvas: any;
  ctx: any;
  selectedSubCategories: any[];
  uniqueWorkAreaArray: any[];
  filteredWorkAreaData: any[] = [];
  WorkAreaData: any;
  constructor(
    private fb: FormBuilder,
    private masterDataService: MasterDataService,
    private router: Router,
    public dialog: MatDialog,
    private sharedData: ShareDataService,
    private auth: AuthService,
    private notify: NotificationService,
    private cd: ChangeDetectorRef,
    private api: InventoryService,
  ) {
    this.user = this.auth.getCurrentUser();
    this.baseData = this.sharedData.getBaseData().value;
    this.newCategory = this.baseData['inventory master'].map(cat => cat.category.toUpperCase());
    this.getCategories();
    this.getLocationCall();
    this.getBaseDataForWorkArea();

    this.vendorMappingForm = this.fb.group({
      vendor: ['', Validators.required],
      category: ['', Validators.required],
      subCategory: ['', Validators.required],
    });

    this.workAreaMappingForm = this.fb.group({
      location: ['', Validators.required],
      workArea: ['', Validators.required],
      category: ['', Validators.required],
      subCategory: ['', Validators.required],
    });

    this.sharedData.getItemNames.pipe(first()).subscribe(obj => {
      this.vendorBank = obj.vendor;
      let uniqueVendorArray = [...new Set(this.vendorBank)];
      this.vendor.next(uniqueVendorArray.slice());
      this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.Filter(this.vendorBank, this.vendorFilterCtrl, this.vendor);
      });
      this.vendorMappingForm.get('vendor').setValue(uniqueVendorArray[0])
      this.getVendorCategories(this.vendorMappingForm.value.vendor)
    });
  }

  ngOnInit() {
    // if(this.vendorMappingForm.value.vendor){
    //   const categories = this.baseData['inventory master']
    //   .filter(item => item.vendor.includes(this.vendorMappingForm.value.vendor))
    //   .map(item => item.category);
    //   let data = [...new Set(categories)];
    //   this.vendorMappingForm.get('category').setValue(data)
    // }
  }

  getLocationCall() {
    this.api.getLocations(this.user.tenantId).pipe(first()).subscribe({
      next: (res) => {
        if (res['result'] == 'success') {
          this.locationList = res['branches'];
          this.locationBank = this.locationList.map(area => area.abbreviatedRestaurantId);
          this.locationBank = [...new Set(this.locationBank)];
          this.location.next(this.locationBank.slice());
          this.locationFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.locationBank, this.locationFilterCtrl, this.location);
          });
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  getCategories() {
    this.api.getCategories({ tenantId: this.user.tenantId, type: 'inventory' }).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.catAndsubCat = res['categories'];
          let categoryData = Object.keys(res['categories']).map(category => category.toUpperCase());
          let newCat = [...this.newCategory, ...categoryData];
          this.categories = [...new Set(newCat)];
          this.catBank = this.categories
          let uniqueArray = [...new Set(this.catBank)];
          this.cat.next(uniqueArray.slice());
          this.catFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.catBank, this.catFilterCtrl, this.cat);
          });
          if(this.vendorMappingForm.value.category){
            this.getSubCategories(this.vendorMappingForm.value.category)
          }
        }
      },
      error: (err) => { console.log(err); }
    });
  }

  getSubCategories(event) {
    this.vendorMappingForm.get('subCategory').setValue('');
    this.filteredCatData = this.baseData['inventory master'].filter(item => event.includes(item.category));
    this.selectedSubCategories = [];
    for (const category of event) {
      if (this.catAndsubCat.hasOwnProperty(category)) {
        this.selectedSubCategories.push({
          category: category,
          subCategories: this.catAndsubCat[category]
        });
      }
    }
    this.subCatBank = this.selectedSubCategories;
    this.subCat.next(this.subCatBank.slice());
    this.subCatFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.FilterSubCategory(this.subCatBank, this.subCatFilterCtrl, this.subCat);
    });
    const allSubCategories = this.selectedSubCategories.flatMap(category => category.subCategories);
    if(this.selectedTabIndex === 1){
      this.workAreaMappingForm.get('subCategory').setValue(allSubCategories)
      this.getFilteredData(this.workAreaMappingForm.value.subCategory) 
    }else{
      this.vendorMappingForm.get('subCategory').setValue(allSubCategories)
      this.getFilteredData(this.vendorMappingForm.value.subCategory) 
    }
  }

  close() {
    this.masterDataService.setNavigation('inventoryList');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  getWorkAreas(value){
    let allWorkAreas
    let val
    const userData = this.locationList.find(item => item.abbreviatedRestaurantId === value);
    if (userData) {
      const branchData = this.WorkAreaData['branches'].find(item => item['abbreviated restaurantId'] === value);
      if (branchData) {
        const { branchName, workArea } = branchData;
        const workAreasSet = new Set(workArea.split(','));
        val = [...workAreasSet];
      } 
    }
    if (val) {
      allWorkAreas = val;
    } else {
      allWorkAreas = [];
    }
    this.workAreaBank = allWorkAreas;
    this.uniqueWorkAreaArray = [...new Set(this.workAreaBank)];
    this.workArea.next(this.workAreaBank.slice());
    this.workAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.Filter(this.workAreaBank, this.workAreaFilterCtrl, this.workArea);
    });
  }

  getVendorCategories(value){
    this.vendorMappingForm.get('vendor').setValue(value)
    if(this.vendorMappingForm.value.vendor){
      const categories = this.baseData['inventory master']
      .filter(item => item.vendor.includes(this.vendorMappingForm.value.vendor))
      .map(item => item.category);
      let data = [...new Set(categories)];
      this.vendorMappingForm.get('category').setValue(data)
    }
    this.getSubCategories(this.vendorMappingForm.value.category);
  }

  getWorkAreaCategory(value){
    this.filteredWorkAreaData = this.baseData['inventory master'].filter(item => item.issuedTo.includes(this.workAreaMappingForm.value.workArea))
    // let data = this.baseData['inventory master'].filter(item => value.includes(item.issuedTo));
    this.workAreaMappingForm.get('workArea').setValue(value)
    if(this.workAreaMappingForm.value.workArea){
      const categories = this.baseData['inventory master']
      .filter(item => item.issuedTo.includes(this.workAreaMappingForm.value.workArea))
      .map(item => item.category);
      let data = [...new Set(categories)];
      this.workAreaMappingForm.get('category').setValue(data)
    }
    this.getSubCategories(this.workAreaMappingForm.value.category);
  }

  getFilteredData(val) {
    this.filteredSubcatData = this.filteredCatData.filter(item => val.includes(item.subCategory));
    this.createChart();
  }

  tabChanged(selectedIndex: number) {
    this.selectedTabIndex = selectedIndex;
    this.filteredCatData = [];
    this.filteredSubcatData = [];
    // this.vendorMappingForm.reset();
    // this.workAreaMappingForm.reset();

    if(this.selectedTabIndex === 0){
      this.getVendorCategories(this.vendorMappingForm.value.vendor)
    }else if(this.selectedTabIndex === 1 && this.workAreaMappingForm.value.location){
      this.getWorkAreas(this.workAreaMappingForm.value.location)
      this.getWorkAreaCategory(this.workAreaMappingForm.value.workArea)
    }

    // if(this.selectedTabIndex === 1){
    //   this.workAreaMappingForm.get('workArea').setValue(this.uniqueWorkAreaArray[0])
    //   if(this.workAreaMappingForm.value.workArea){
    //     const categories = this.baseData['inventory master']
    //     .filter(item => item.issuedTo.includes(this.workAreaMappingForm.value.workArea))
    //     .map(item => item.category);
    //     let data = [...new Set(categories)];
    //     this.workAreaMappingForm.get('category').setValue(data)
    //   }
    //   this.getSubCategories(this.workAreaMappingForm.value.category);
    // }
  }

  triggerChart(){
    this.createChart();
  }

  mapping() {
    let data
    let formValue
    if (this.selectedTabIndex === 1) {
      data = 'issuedTo'
      formValue = this.workAreaMappingForm.value.workArea
    } else {
      data = 'vendor'
      formValue = this.vendorMappingForm.value.vendor
    }
    this.filteredSubcatData.forEach(item => {
      if (item.discontinued === "no" || item.discontinued === "NO" || item.discontinued === "No" || !item.discontinued) {
        if (item[data]) {
          let items = item[data].split(',');
          if (!items.includes(formValue)) {
            items.push(formValue);
          }
          item[data] = items.join(',');
        } else {
          item[data] = formValue;
        }
        item.modified = "yes";
      }
    });
    this.updateInventory()
  }

  updateInventory() {
    this.baseData = this.sharedData.getBaseData().value
    let itemCode = this.filteredSubcatData.map(item => item.itemCode)
    const packageData = this.baseData['packagingmasters'].filter(item => itemCode.includes(item.InventoryCode));
    let current = {}
    current['inventory master'] = this.filteredSubcatData
    current['packagingmasters'] = packageData
    this.api.updateData({
      'tenantId': this.user.tenantId,
      'userEmail': this.user.email,
      'type': 'inventory',
      'data': current
    }).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.cd.detectChanges();
          this.notify.snackBarShowSuccess('The mapping has been successfully completed.');
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  allSelectCategory(form) {
    let control
    if(form === 'vendorMappingForm'){
      control = this.vendorMappingForm.controls['category'];
      if (control.value.length - 1 === this.catBank.length) {
        control.setValue([]);
        this.getSubCategories(this.vendorMappingForm.value.category);
      } else {
        control.setValue(this.catBank);
        this.getSubCategories(this.vendorMappingForm.value.category);
      }
      this.filteredCatData = this.baseData['inventory master'].filter(item => this.vendorMappingForm.value.category.includes(item.category));
    }else if(form === 'workAreaMappingForm'){
      control = this.workAreaMappingForm.controls['category'];
      if (control.value.length - 1 === this.catBank.length) {
        control.setValue([]);
        this.getSubCategories(this.workAreaMappingForm.value.category);
      } else {
        control.setValue(this.catBank);
        this.getSubCategories(this.workAreaMappingForm.value.category);
      }
      this.filteredCatData = this.baseData['inventory master'].filter(item => this.workAreaMappingForm.value.category.includes(item.category));
    }
    this.createChart();
  }

  allSelectSubCategory(form) {
    let control
    if(form === 'vendorMappingForm'){
      control = this.vendorMappingForm.controls['subCategory'];
      let data = [...this.subCatBank.map(val => val.subCategories)];
      const flattenedArray = [].concat(...data);
      if (control.value.length - 1 === flattenedArray.length) {
        control.setValue([]);
      } else {
        control.setValue(flattenedArray);
      }
      this.filteredSubcatData = this.filteredCatData.filter(item => this.vendorMappingForm.value.subCategory.includes(item.subCategory));
    }else if(form === 'workAreaMappingForm'){
      control = this.workAreaMappingForm.controls['subCategory'];
      let data = [...this.subCatBank.map(val => val.subCategories)];
      const flattenedArray = [].concat(...data);
      if (control.value.length - 1 === flattenedArray.length) {
        control.setValue([]);
      } else {
        control.setValue(flattenedArray);
      }
      this.filteredSubcatData = this.filteredCatData.filter(item => this.workAreaMappingForm.value.subCategory.includes(item.subCategory));
    }
    this.createChart();
  }

  protected Filter(bank: any, form: any, data: any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(data => data.toLowerCase().indexOf(search) > -1)
    );
  }

  protected FilterSubCategory(bank:any, form:any, data:any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    const filteredBank = bank.map(item => {
      const filteredWorkAreas = item.subCategories.filter(val => val.toLowerCase().indexOf(search) > -1);
      return { ...item, subCategories: filteredWorkAreas };
      
    });
    data.next(filteredBank);
  }

  ngAfterViewInit(): void {
    this.isViewInitialized = true;
    this.createChart();
  }

  setChartValue(value: number): void {
    if (this.isViewInitialized) {
      if (this.chart && this.chart.data && this.chart.data.datasets) {
        this.chart.data.datasets[0].data[0] = value;
        this.chart.update();
      }
    } else {
      console.warn('View is not initialized yet.');
    }
  }

  createChart() {
    if (this.chart) {
      this.chart.destroy(); 
    }
    if(this.barChart && this.barChart.nativeElement){
      this.chart = new Chart(this.barChart.nativeElement, {
        type: 'bar',
        data: {
          labels: ['Total Items', 'Category Items', 'Subcategory Items'],
          datasets: [{
            label: 'Inventory Items',
            data: [this.baseData['inventory master'].length, this.filteredCatData.length, this.filteredSubcatData.length],
            backgroundColor: [
              'rgba(255, 99, 132, 0.2)',
              'rgba(54, 162, 235, 0.2)',
              'rgba(255, 206, 86, 0.2)'
            ],
            borderColor: [
              'rgba(255, 99, 132, 1)',
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          plugins: {
            legend: {
                display: false
            }
        },
          scales: {
            x: {
              beginAtZero: true
            }
          }
        },

      });
    }
  }

  getBaseDataForWorkArea() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['userEmail'] = this.user.email
    obj['type'] = 'user'
    obj['specific'] = 'branches'
    this.api.getPresentData(obj).pipe(first()).subscribe({next: (res) => {
        this.WorkAreaData = res['data'][0] ?? res['data'];
      },
      error: (err) => { console.log(err) }
    })
  }
}
