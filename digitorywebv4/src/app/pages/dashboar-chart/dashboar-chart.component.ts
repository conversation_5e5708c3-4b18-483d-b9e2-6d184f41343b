import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InventoryService } from 'src/app/services/inventory.service';
import { Router } from '@angular/router';
import { ShareDataService } from 'src/app/services/share-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { MasterDataService } from 'src/app/services/master-data.service';
import { MatCardModule } from '@angular/material/card';
import { Chart,registerables } from 'node_modules/chart.js';
Chart.register(...registerables)

@Component({
  selector: 'app-dashboar-chart',
  standalone: true,
  imports: [CommonModule,MatCardModule],
  templateUrl: './dashboar-chart.component.html',
  styleUrls: ['./dashboar-chart.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboarChartComponent {
  user: any;
  baseData: any;

  constructor(private api: InventoryService,private router: Router,private sharedData: ShareDataService, private cd: ChangeDetectorRef,
    private auth: AuthService,
    private masterDataService: MasterDataService,
  ) {
    this.user = this.auth.getCurrentUser();
    this.getBaseData()
  }

getBaseData(){
  let obj = {}
  obj['tenantId'] = this.user.tenantId
  obj['userEmail'] = this.user.email
  obj['type'] = 'inventory'
  this.api.getPresentData(obj).subscribe({
    next: (res) => {
      if (res['success'] && ( res['data'].length > 0 || Object.keys(res['data']).length > 0)) {
        this.baseData = res['data'][0] ?? res['data'];
        this.renderChart();
      } else {
        this.baseData = [];
      }
    },

  })
}

renderChart(){
  const myChart = new Chart("pieChart",{
    type : "bar",
    data : {
      labels : ['red', 'blue'],
      datasets : [
        {
          label : "## ",
          data : ['1','2','3'],
          backgroundColor : ['red','blue','green'],
          borderColor: ['red','blue','green'],
          borderWidth : 1
        }
      ]

    },
    options : {
      scales : {
        y : {
          beginAtZero : true
        }
      }
    }
   })
}

}
